import React, { useState } from "react";
import {
    View,
    Text,
    TextInput,
    TouchableOpacity,
    Modal,
    ScrollView,
    StyleSheet,
    Alert,
    KeyboardAvoidingView,
    Platform,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";

interface AddWordModalProps {
    visible: boolean;
    onClose: () => void;
    onAddWord: (wordData: WordData) => void;
}

export interface WordData {
    word: string;
    meaning: string;
    pronunciation?: string;
    partOfSpeech: string;
    examples: string[];
    synonyms: string[];
    difficulty: "beginner" | "intermediate" | "advanced";
}

const AddWordModal: React.FC<AddWordModalProps> = ({ visible, onClose, onAddWord }) => {
    const [formData, setFormData] = useState<WordData>({
        word: "",
        meaning: "",
        pronunciation: "",
        partOfSpeech: "noun",
        examples: [""],
        synonyms: [""],
        difficulty: "intermediate",
    });

    const [errors, setErrors] = useState<{ [key: string]: string }>({});

    const partOfSpeechOptions = [
        { label: "İsim", value: "noun" },
        { label: "Fiil", value: "verb" },
        { label: "Sıfat", value: "adjective" },
        { label: "Zarf", value: "adverb" },
        { label: "Edat", value: "preposition" },
        { label: "Diğer", value: "other" },
    ];

    const difficultyOptions = [
        { label: "Başlangıç", value: "beginner" },
        { label: "Orta", value: "intermediate" },
        { label: "İleri", value: "advanced" },
    ];

    const validateForm = (): boolean => {
        const newErrors: { [key: string]: string } = {};

        if (!formData.word.trim()) {
            newErrors.word = "Kelime gerekli";
        }

        if (!formData.meaning.trim()) {
            newErrors.meaning = "Anlam gerekli";
        }

        if (formData.examples.filter((ex) => ex.trim()).length === 0) {
            newErrors.examples = "En az bir örnek cümle gerekli";
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = () => {
        if (!validateForm()) return;

        const cleanedData: WordData = {
            ...formData,
            word: formData.word.trim(),
            meaning: formData.meaning.trim(),
            pronunciation: formData.pronunciation?.trim(),
            examples: formData.examples.filter((ex) => ex.trim()),
            synonyms: formData.synonyms.filter((syn) => syn.trim()),
        };

        onAddWord(cleanedData);
        resetForm();
        onClose();
    };

    const resetForm = () => {
        setFormData({
            word: "",
            meaning: "",
            pronunciation: "",
            partOfSpeech: "noun",
            examples: [""],
            synonyms: [""],
            difficulty: "intermediate",
        });
        setErrors({});
    };

    const addExample = () => {
        setFormData((prev) => ({
            ...prev,
            examples: [...prev.examples, ""],
        }));
    };

    const updateExample = (index: number, value: string) => {
        setFormData((prev) => ({
            ...prev,
            examples: prev.examples.map((ex, i) => (i === index ? value : ex)),
        }));
    };

    const removeExample = (index: number) => {
        if (formData.examples.length > 1) {
            setFormData((prev) => ({
                ...prev,
                examples: prev.examples.filter((_, i) => i !== index),
            }));
        }
    };

    const addSynonym = () => {
        setFormData((prev) => ({
            ...prev,
            synonyms: [...prev.synonyms, ""],
        }));
    };

    const updateSynonym = (index: number, value: string) => {
        setFormData((prev) => ({
            ...prev,
            synonyms: prev.synonyms.map((syn, i) => (i === index ? value : syn)),
        }));
    };

    const removeSynonym = (index: number) => {
        if (formData.synonyms.length > 1) {
            setFormData((prev) => ({
                ...prev,
                synonyms: prev.synonyms.filter((_, i) => i !== index),
            }));
        }
    };

    return (
        <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
            <LinearGradient colors={["#f8fafc", "#e2e8f0"]} style={styles.container}>
                <KeyboardAvoidingView
                    behavior={Platform.OS === "ios" ? "padding" : "height"}
                    style={styles.container}
                >
                    <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
                        <View style={styles.content}>
                            {/* Header */}
                            <View style={styles.header}>
                                <TouchableOpacity style={styles.closeButton} onPress={onClose}>
                                    <Text style={styles.closeButtonText}>✕</Text>
                                </TouchableOpacity>
                                <Text style={styles.title}>Yeni Kelime Ekle</Text>
                                <Text style={styles.subtitle}>
                                    Kelime dağarcığınıza yeni bir kelime ekleyin
                                </Text>
                            </View>

                            {/* Form */}
                            <View style={styles.formContainer}>
                                {/* Word Input */}
                                <View style={styles.inputContainer}>
                                    <Text style={styles.label}>Kelime *</Text>
                                    <TextInput
                                        style={[styles.input, errors.word && styles.inputError]}
                                        placeholder="Örn: serendipity"
                                        value={formData.word}
                                        onChangeText={(text) =>
                                            setFormData((prev) => ({ ...prev, word: text }))
                                        }
                                        placeholderTextColor="#9CA3AF"
                                    />
                                    {errors.word && (
                                        <Text style={styles.errorText}>{errors.word}</Text>
                                    )}
                                </View>

                                {/* Meaning Input */}
                                <View style={styles.inputContainer}>
                                    <Text style={styles.label}>Anlam *</Text>
                                    <TextInput
                                        style={[styles.input, errors.meaning && styles.inputError]}
                                        placeholder="Türkçe anlamı"
                                        value={formData.meaning}
                                        onChangeText={(text) =>
                                            setFormData((prev) => ({ ...prev, meaning: text }))
                                        }
                                        placeholderTextColor="#9CA3AF"
                                    />
                                    {errors.meaning && (
                                        <Text style={styles.errorText}>{errors.meaning}</Text>
                                    )}
                                </View>

                                {/* Pronunciation Input */}
                                <View style={styles.inputContainer}>
                                    <Text style={styles.label}>Telaffuz</Text>
                                    <TextInput
                                        style={styles.input}
                                        placeholder="Örn: /ˌserənˈdipədē/"
                                        value={formData.pronunciation}
                                        onChangeText={(text) =>
                                            setFormData((prev) => ({
                                                ...prev,
                                                pronunciation: text,
                                            }))
                                        }
                                        placeholderTextColor="#9CA3AF"
                                    />
                                </View>

                                {/* Part of Speech */}
                                <View style={styles.inputContainer}>
                                    <Text style={styles.label}>Kelime Türü</Text>
                                    <ScrollView
                                        horizontal
                                        showsHorizontalScrollIndicator={false}
                                        style={styles.optionsContainer}
                                    >
                                        {partOfSpeechOptions.map((option) => (
                                            <TouchableOpacity
                                                key={option.value}
                                                style={[
                                                    styles.optionButton,
                                                    formData.partOfSpeech === option.value &&
                                                        styles.selectedOption,
                                                ]}
                                                onPress={() =>
                                                    setFormData((prev) => ({
                                                        ...prev,
                                                        partOfSpeech: option.value,
                                                    }))
                                                }
                                            >
                                                <Text
                                                    style={[
                                                        styles.optionText,
                                                        formData.partOfSpeech === option.value &&
                                                            styles.selectedOptionText,
                                                    ]}
                                                >
                                                    {option.label}
                                                </Text>
                                            </TouchableOpacity>
                                        ))}
                                    </ScrollView>
                                </View>

                                {/* Difficulty */}
                                <View style={styles.inputContainer}>
                                    <Text style={styles.label}>Zorluk Seviyesi</Text>
                                    <ScrollView
                                        horizontal
                                        showsHorizontalScrollIndicator={false}
                                        style={styles.optionsContainer}
                                    >
                                        {difficultyOptions.map((option) => (
                                            <TouchableOpacity
                                                key={option.value}
                                                style={[
                                                    styles.optionButton,
                                                    formData.difficulty === option.value &&
                                                        styles.selectedOption,
                                                ]}
                                                onPress={() =>
                                                    setFormData((prev) => ({
                                                        ...prev,
                                                        difficulty: option.value as any,
                                                    }))
                                                }
                                            >
                                                <Text
                                                    style={[
                                                        styles.optionText,
                                                        formData.difficulty === option.value &&
                                                            styles.selectedOptionText,
                                                    ]}
                                                >
                                                    {option.label}
                                                </Text>
                                            </TouchableOpacity>
                                        ))}
                                    </ScrollView>
                                </View>

                                {/* Examples */}
                                <View style={styles.inputContainer}>
                                    <View style={styles.sectionHeader}>
                                        <Text style={styles.label}>Örnek Cümleler *</Text>
                                        <TouchableOpacity
                                            style={styles.addButton}
                                            onPress={addExample}
                                        >
                                            <Text style={styles.addButtonText}>+ Ekle</Text>
                                        </TouchableOpacity>
                                    </View>
                                    {formData.examples.map((example, index) => (
                                        <View key={index} style={styles.listItemContainer}>
                                            <TextInput
                                                style={[styles.input, styles.listInput]}
                                                placeholder={`Örnek cümle ${index + 1}`}
                                                value={example}
                                                onChangeText={(text) => updateExample(index, text)}
                                                placeholderTextColor="#9CA3AF"
                                                multiline
                                            />
                                            {formData.examples.length > 1 && (
                                                <TouchableOpacity
                                                    style={styles.removeButton}
                                                    onPress={() => removeExample(index)}
                                                >
                                                    <Text style={styles.removeButtonText}>✕</Text>
                                                </TouchableOpacity>
                                            )}
                                        </View>
                                    ))}
                                    {errors.examples && (
                                        <Text style={styles.errorText}>{errors.examples}</Text>
                                    )}
                                </View>

                                {/* Synonyms */}
                                <View style={styles.inputContainer}>
                                    <View style={styles.sectionHeader}>
                                        <Text style={styles.label}>Eş Anlamlılar</Text>
                                        <TouchableOpacity
                                            style={styles.addButton}
                                            onPress={addSynonym}
                                        >
                                            <Text style={styles.addButtonText}>+ Ekle</Text>
                                        </TouchableOpacity>
                                    </View>
                                    {formData.synonyms.map((synonym, index) => (
                                        <View key={index} style={styles.listItemContainer}>
                                            <TextInput
                                                style={[styles.input, styles.listInput]}
                                                placeholder={`Eş anlamlı ${index + 1}`}
                                                value={synonym}
                                                onChangeText={(text) => updateSynonym(index, text)}
                                                placeholderTextColor="#9CA3AF"
                                            />
                                            {formData.synonyms.length > 1 && (
                                                <TouchableOpacity
                                                    style={styles.removeButton}
                                                    onPress={() => removeSynonym(index)}
                                                >
                                                    <Text style={styles.removeButtonText}>✕</Text>
                                                </TouchableOpacity>
                                            )}
                                        </View>
                                    ))}
                                </View>

                                {/* Submit Buttons */}
                                <View style={styles.buttonContainer}>
                                    <TouchableOpacity style={styles.cancelButton} onPress={onClose}>
                                        <Text style={styles.cancelButtonText}>İptal</Text>
                                    </TouchableOpacity>
                                    <TouchableOpacity
                                        style={styles.submitButton}
                                        onPress={handleSubmit}
                                    >
                                        <LinearGradient
                                            colors={["#3B82F6", "#1E40AF"]}
                                            style={styles.submitGradient}
                                        >
                                            <Text style={styles.submitButtonText}>Kelime Ekle</Text>
                                        </LinearGradient>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                    </ScrollView>
                </KeyboardAvoidingView>
            </LinearGradient>
        </Modal>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    scrollView: {
        flex: 1,
    },
    content: {
        paddingHorizontal: 24,
        paddingTop: 20,
        paddingBottom: 40,
    },
    header: {
        alignItems: "center",
        marginBottom: 32,
        position: "relative",
    },
    closeButton: {
        position: "absolute",
        top: 0,
        right: 0,
        backgroundColor: "rgba(0, 0, 0, 0.1)",
        borderRadius: 20,
        width: 40,
        height: 40,
        alignItems: "center",
        justifyContent: "center",
    },
    closeButtonText: {
        fontSize: 18,
        color: "#6B7280",
        fontWeight: "600",
    },
    title: {
        fontSize: 28,
        fontWeight: "bold",
        color: "#1F2937",
        marginBottom: 8,
    },
    subtitle: {
        fontSize: 16,
        color: "#6B7280",
        textAlign: "center",
    },
    formContainer: {
        backgroundColor: "white",
        borderRadius: 20,
        padding: 24,
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 0.1,
        shadowRadius: 16,
        elevation: 5,
    },
    inputContainer: {
        marginBottom: 20,
    },
    label: {
        fontSize: 16,
        fontWeight: "600",
        color: "#374151",
        marginBottom: 8,
    },
    input: {
        borderWidth: 2,
        borderColor: "#E5E7EB",
        borderRadius: 12,
        paddingHorizontal: 16,
        paddingVertical: 12,
        fontSize: 16,
        backgroundColor: "#F9FAFB",
        color: "#1F2937",
    },
    inputError: {
        borderColor: "#EF4444",
    },
    errorText: {
        color: "#EF4444",
        fontSize: 14,
        marginTop: 4,
    },
    optionsContainer: {
        flexDirection: "row",
        marginTop: 8,
    },
    optionButton: {
        backgroundColor: "#F3F4F6",
        borderRadius: 20,
        paddingHorizontal: 16,
        paddingVertical: 8,
        marginRight: 8,
        borderWidth: 2,
        borderColor: "transparent",
    },
    selectedOption: {
        backgroundColor: "#DBEAFE",
        borderColor: "#3B82F6",
    },
    optionText: {
        fontSize: 14,
        color: "#6B7280",
        fontWeight: "500",
    },
    selectedOptionText: {
        color: "#1E40AF",
        fontWeight: "600",
    },
    sectionHeader: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        marginBottom: 12,
    },
    addButton: {
        backgroundColor: "#3B82F6",
        borderRadius: 8,
        paddingHorizontal: 12,
        paddingVertical: 6,
    },
    addButtonText: {
        color: "white",
        fontSize: 12,
        fontWeight: "600",
    },
    listItemContainer: {
        flexDirection: "row",
        alignItems: "flex-start",
        marginBottom: 8,
    },
    listInput: {
        flex: 1,
        marginRight: 8,
    },
    removeButton: {
        backgroundColor: "#FEE2E2",
        borderRadius: 16,
        width: 32,
        height: 32,
        alignItems: "center",
        justifyContent: "center",
        marginTop: 6,
    },
    removeButtonText: {
        color: "#EF4444",
        fontSize: 14,
        fontWeight: "600",
    },
    buttonContainer: {
        flexDirection: "row",
        justifyContent: "space-between",
        marginTop: 24,
        gap: 12,
    },
    cancelButton: {
        flex: 1,
        backgroundColor: "#F3F4F6",
        borderRadius: 12,
        paddingVertical: 16,
        alignItems: "center",
    },
    cancelButtonText: {
        color: "#6B7280",
        fontSize: 16,
        fontWeight: "600",
    },
    submitButton: {
        flex: 1,
        borderRadius: 12,
        overflow: "hidden",
    },
    submitGradient: {
        paddingVertical: 16,
        alignItems: "center",
    },
    submitButtonText: {
        color: "white",
        fontSize: 16,
        fontWeight: "600",
    },
});

export default AddWordModal;
