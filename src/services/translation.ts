// Translation service using OpenAI and Google Translate APIs

export interface TranslationResult {
  word: string;
  meaning: string;
  pronunciation?: string;
  partOfSpeech?: string;
  examples: string[];
  synonyms?: string[];
  difficulty?: 'beginner' | 'intermediate' | 'advanced';
}

export interface TranslationService {
  translateWord: (word: string, targetLanguage?: string) => Promise<TranslationResult>;
  translateText: (text: string, targetLanguage?: string) => Promise<string>;
  getWordDefinition: (word: string) => Promise<TranslationResult>;
}

class OpenAITranslationService implements TranslationService {
  private apiKey: string;
  private baseUrl = 'https://api.openai.com/v1/chat/completions';

  constructor() {
    this.apiKey = process.env.EXPO_PUBLIC_OPENAI_API_KEY || '';
  }

  async translateWord(word: string, targetLanguage = 'Turkish'): Promise<TranslationResult> {
    if (!this.apiKey) {
      throw new Error('OpenAI API key not configured');
    }

    const prompt = `
      Translate the English word "${word}" to ${targetLanguage} and provide detailed information.
      Return the response in the following JSON format:
      {
        "word": "${word}",
        "meaning": "Turkish translation",
        "pronunciation": "phonetic pronunciation",
        "partOfSpeech": "noun/verb/adjective/etc",
        "examples": ["example sentence 1", "example sentence 2"],
        "synonyms": ["synonym1", "synonym2"],
        "difficulty": "beginner/intermediate/advanced"
      }
    `;

    try {
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`,
        },
        body: JSON.stringify({
          model: 'gpt-3.5-turbo',
          messages: [
            {
              role: 'system',
              content: 'You are a helpful language learning assistant. Always respond with valid JSON.'
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          max_tokens: 500,
          temperature: 0.3,
        }),
      });

      if (!response.ok) {
        throw new Error(`OpenAI API error: ${response.status}`);
      }

      const data = await response.json();
      const content = data.choices[0]?.message?.content;
      
      if (!content) {
        throw new Error('No response from OpenAI');
      }

      return JSON.parse(content);
    } catch (error) {
      console.error('OpenAI translation error:', error);
      // Fallback to mock data for development
      return this.getMockTranslation(word);
    }
  }

  async translateText(text: string, targetLanguage = 'Turkish'): Promise<string> {
    if (!this.apiKey) {
      throw new Error('OpenAI API key not configured');
    }

    const prompt = `Translate the following English text to ${targetLanguage}: "${text}"`;

    try {
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`,
        },
        body: JSON.stringify({
          model: 'gpt-3.5-turbo',
          messages: [
            {
              role: 'system',
              content: 'You are a professional translator. Provide only the translation without any additional text.'
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          max_tokens: 200,
          temperature: 0.1,
        }),
      });

      if (!response.ok) {
        throw new Error(`OpenAI API error: ${response.status}`);
      }

      const data = await response.json();
      return data.choices[0]?.message?.content || text;
    } catch (error) {
      console.error('OpenAI text translation error:', error);
      return `[Çeviri hatası: ${text}]`;
    }
  }

  async getWordDefinition(word: string): Promise<TranslationResult> {
    return this.translateWord(word);
  }

  private getMockTranslation(word: string): TranslationResult {
    // Mock data for development/testing
    const mockTranslations: { [key: string]: TranslationResult } = {
      'serendipity': {
        word: 'serendipity',
        meaning: 'tesadüf, şans eseri karşılaşma',
        pronunciation: '/ˌserənˈdipədē/',
        partOfSpeech: 'noun',
        examples: [
          'It was pure serendipity that we met at the coffee shop.',
          'The discovery was a result of serendipity rather than planning.'
        ],
        synonyms: ['chance', 'fortune', 'luck'],
        difficulty: 'intermediate'
      },
      'ephemeral': {
        word: 'ephemeral',
        meaning: 'geçici, kısa süreli',
        pronunciation: '/əˈfem(ə)rəl/',
        partOfSpeech: 'adjective',
        examples: [
          'The beauty of cherry blossoms is ephemeral.',
          'Social media trends are often ephemeral.'
        ],
        synonyms: ['temporary', 'fleeting', 'transient'],
        difficulty: 'advanced'
      }
    };

    return mockTranslations[word.toLowerCase()] || {
      word,
      meaning: `[${word} için çeviri bulunamadı]`,
      examples: [`Example sentence with ${word}.`],
      difficulty: 'intermediate'
    };
  }
}

class GoogleTranslateService implements TranslationService {
  private apiKey: string;
  private baseUrl = 'https://translation.googleapis.com/language/translate/v2';

  constructor() {
    this.apiKey = process.env.EXPO_PUBLIC_GOOGLE_TRANSLATE_API_KEY || '';
  }

  async translateWord(word: string, targetLanguage = 'tr'): Promise<TranslationResult> {
    const translation = await this.translateText(word, targetLanguage);
    
    return {
      word,
      meaning: translation,
      examples: [`Example sentence with ${word}.`],
      difficulty: 'intermediate'
    };
  }

  async translateText(text: string, targetLanguage = 'tr'): Promise<string> {
    if (!this.apiKey) {
      throw new Error('Google Translate API key not configured');
    }

    try {
      const response = await fetch(`${this.baseUrl}?key=${this.apiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          q: text,
          target: targetLanguage,
          source: 'en',
        }),
      });

      if (!response.ok) {
        throw new Error(`Google Translate API error: ${response.status}`);
      }

      const data = await response.json();
      return data.data.translations[0].translatedText;
    } catch (error) {
      console.error('Google Translate error:', error);
      return `[Çeviri hatası: ${text}]`;
    }
  }

  async getWordDefinition(word: string): Promise<TranslationResult> {
    return this.translateWord(word);
  }
}

// Export the service instance
export const translationService: TranslationService = new OpenAITranslationService();

// Alternative service for fallback
export const googleTranslateService: TranslationService = new GoogleTranslateService();

// Utility function for easy access
export const translateWord = async (word: string): Promise<TranslationResult> => {
  return translationService.translateWord(word);
};
