import React, { useState } from "react";
import {
    View,
    Text,
    TouchableOpacity,
    ScrollView,
    StyleSheet,
    SafeAreaView,
    StatusBar,
    Dimensions,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { StackNavigationProp } from "@react-navigation/stack";
import { RouteProp } from "@react-navigation/native";
import { ReadingStackParamList } from "../../types/navigation";
import { useHideTabNavigation } from "../../hooks/useHideTabNavigation";

type ReadingViewScreenNavigationProp = StackNavigationProp<ReadingStackParamList, "ReadingView">;
type ReadingViewScreenRouteProp = RouteProp<ReadingStackParamList, "ReadingView">;

interface ReadingViewScreenProps {
    navigation: ReadingViewScreenNavigationProp;
    route: ReadingViewScreenRouteProp;
}

const ReadingViewScreen: React.FC<ReadingViewScreenProps> = ({ navigation, route }) => {
    const { bookTitle, bookId, level } = route.params;
    const [currentPage, setCurrentPage] = useState(1);
    const [isEditMode, setIsEditMode] = useState(false);

    // Hide main tab navigation when this screen is focused
    useHideTabNavigation({ navigation });

    // Mock book content - gerçek uygulamada API'den gelecek
    const bookContent = {
        title: bookTitle,
        totalPages: 45,
        currentPageContent: `Chapter 1: The Beginning

Once upon a time, in a small village nestled between rolling hills and a sparkling river, there lived a young girl named Emma. She had always been curious about the world beyond her village, dreaming of adventures that awaited her.

Every morning, Emma would wake up early and walk to the old oak tree at the edge of the village. From there, she could see the vast forest that stretched endlessly toward the horizon. The forest seemed to whisper secrets that only she could hear.

One particular morning, as the golden sunlight filtered through the leaves, Emma noticed something unusual. A small, glowing butterfly had landed on her shoulder. Its wings shimmered with colors she had never seen before - deep purples, brilliant golds, and silvery blues that seemed to dance in the light.

"Hello, little one," Emma whispered softly, afraid that speaking too loudly might frighten the magical creature away. To her amazement, the butterfly seemed to understand her words.

The butterfly fluttered its wings gently and began to fly in a slow circle around Emma's head. Then, as if inviting her to follow, it started flying toward the mysterious forest.

Emma hesitated for a moment. She had been told many times never to enter the forest alone. But something about this butterfly felt different, special, as if it was meant to guide her on an important journey.

Taking a deep breath and gathering her courage, Emma decided to follow the glowing butterfly into the unknown depths of the forest, not knowing that this decision would change her life forever.`,
    };

    const handleGoBack = () => {
        navigation.goBack();
    };

    const handleEditMode = () => {
        setIsEditMode(!isEditMode);
    };

    const handlePageTurn = (direction: "next" | "prev") => {
        if (direction === "next" && currentPage < bookContent.totalPages) {
            setCurrentPage(currentPage + 1);
        } else if (direction === "prev" && currentPage > 1) {
            setCurrentPage(currentPage - 1);
        }
    };

    return (
        <SafeAreaView style={styles.container}>
            <StatusBar barStyle="dark-content" backgroundColor="#f8f9fa" />

            {/* Fixed Header with Back and Edit buttons */}
            <View style={styles.header}>
                <TouchableOpacity style={styles.headerButton} onPress={handleGoBack}>
                    <Text style={styles.headerButtonText}>← Geri</Text>
                </TouchableOpacity>

                <View style={styles.headerCenter}>
                    <Text style={styles.headerTitle}>{bookTitle}</Text>
                    <Text style={styles.headerSubtitle}>
                        Sayfa {currentPage} / {bookContent.totalPages}
                    </Text>
                </View>

                <TouchableOpacity
                    style={[styles.headerButton, isEditMode && styles.activeEditButton]}
                    onPress={handleEditMode}
                >
                    <Text
                        style={[styles.headerButtonText, isEditMode && styles.activeEditButtonText]}
                    >
                        ✏️ Düzenle
                    </Text>
                </TouchableOpacity>
            </View>

            {/* Book Page Container */}
            <View style={styles.bookContainer}>
                <LinearGradient
                    colors={["#ffffff", "#f8f9fa"]}
                    style={styles.bookPage}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 0, y: 1 }}
                >
                    {/* Page Shadow Effect */}
                    <View style={styles.pageShadow} />

                    {/* Book Content */}
                    <ScrollView
                        style={styles.contentContainer}
                        showsVerticalScrollIndicator={false}
                        contentContainerStyle={styles.contentPadding}
                    >
                        <Text style={styles.bookText}>{bookContent.currentPageContent}</Text>
                    </ScrollView>

                    {/* Page Navigation */}
                    <View style={styles.pageNavigation}>
                        <TouchableOpacity
                            style={[styles.navButton, currentPage === 1 && styles.disabledButton]}
                            onPress={() => handlePageTurn("prev")}
                            disabled={currentPage === 1}
                        >
                            <Text
                                style={[
                                    styles.navButtonText,
                                    currentPage === 1 && styles.disabledButtonText,
                                ]}
                            >
                                ← Önceki
                            </Text>
                        </TouchableOpacity>

                        <View style={styles.pageIndicator}>
                            <Text style={styles.pageNumber}>{currentPage}</Text>
                        </View>

                        <TouchableOpacity
                            style={[
                                styles.navButton,
                                currentPage === bookContent.totalPages && styles.disabledButton,
                            ]}
                            onPress={() => handlePageTurn("next")}
                            disabled={currentPage === bookContent.totalPages}
                        >
                            <Text
                                style={[
                                    styles.navButtonText,
                                    currentPage === bookContent.totalPages &&
                                        styles.disabledButtonText,
                                ]}
                            >
                                Sonraki →
                            </Text>
                        </TouchableOpacity>
                    </View>
                </LinearGradient>
            </View>

            {/* Edit Mode Overlay */}
            {isEditMode && (
                <View style={styles.editOverlay}>
                    <View style={styles.editPanel}>
                        <Text style={styles.editTitle}>Okuma Ayarları</Text>
                        <TouchableOpacity style={styles.editOption}>
                            <Text style={styles.editOptionText}>📝 Kelime Çevir</Text>
                        </TouchableOpacity>
                        <TouchableOpacity style={styles.editOption}>
                            <Text style={styles.editOptionText}>🔖 İşaretle</Text>
                        </TouchableOpacity>
                        <TouchableOpacity style={styles.editOption}>
                            <Text style={styles.editOptionText}>📋 Not Ekle</Text>
                        </TouchableOpacity>
                        <TouchableOpacity style={styles.editOption}>
                            <Text style={styles.editOptionText}>🎯 Kelime Listesine Ekle</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            )}
        </SafeAreaView>
    );
};

const { width, height } = Dimensions.get("window");

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: "#f0f2f5",
    },
    header: {
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between",
        paddingHorizontal: 20,
        paddingVertical: 12,
        backgroundColor: "rgba(248, 249, 250, 0.95)",
        borderBottomWidth: 1,
        borderBottomColor: "#e9ecef",
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
        position: "absolute",
        top: 0,
        left: 0,
        right: 0,
        zIndex: 1000,
    },
    headerButton: {
        backgroundColor: "#ffffff",
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderRadius: 20,
        borderWidth: 1,
        borderColor: "#dee2e6",
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 2,
    },
    activeEditButton: {
        backgroundColor: "#667eea",
        borderColor: "#667eea",
    },
    headerButtonText: {
        fontSize: 14,
        fontWeight: "600",
        color: "#495057",
    },
    activeEditButtonText: {
        color: "#ffffff",
    },
    headerCenter: {
        flex: 1,
        alignItems: "center",
        marginHorizontal: 16,
    },
    headerTitle: {
        fontSize: 16,
        fontWeight: "bold",
        color: "#212529",
        textAlign: "center",
    },
    headerSubtitle: {
        fontSize: 12,
        color: "#6c757d",
        marginTop: 2,
    },
    bookContainer: {
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
        paddingTop: 80, // Space for fixed header
        paddingHorizontal: 20,
        paddingBottom: 20,
    },
    bookPage: {
        width: width * 0.9,
        height: height * 0.8,
        borderRadius: 12,
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 0.15,
        shadowRadius: 20,
        elevation: 10,
        position: "relative",
    },
    pageShadow: {
        position: "absolute",
        top: 0,
        left: 8,
        right: 0,
        bottom: 0,
        backgroundColor: "#e9ecef",
        borderRadius: 12,
        zIndex: -1,
    },
    contentContainer: {
        flex: 1,
    },
    contentPadding: {
        padding: 24,
        paddingBottom: 80, // Space for page navigation
    },
    bookText: {
        fontSize: 16,
        lineHeight: 24,
        color: "#212529",
        fontFamily: "System",
        textAlign: "justify",
    },
    pageNavigation: {
        position: "absolute",
        bottom: 0,
        left: 0,
        right: 0,
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        padding: 20,
        backgroundColor: "rgba(255, 255, 255, 0.9)",
        borderBottomLeftRadius: 12,
        borderBottomRightRadius: 12,
    },
    navButton: {
        backgroundColor: "#667eea",
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderRadius: 16,
    },
    disabledButton: {
        backgroundColor: "#e9ecef",
    },
    navButtonText: {
        color: "#ffffff",
        fontSize: 14,
        fontWeight: "600",
    },
    disabledButtonText: {
        color: "#adb5bd",
    },
    pageIndicator: {
        backgroundColor: "#f8f9fa",
        borderRadius: 20,
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderWidth: 1,
        borderColor: "#dee2e6",
    },
    pageNumber: {
        fontSize: 16,
        fontWeight: "bold",
        color: "#495057",
    },
    editOverlay: {
        position: "absolute",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: "rgba(0, 0, 0, 0.5)",
        justifyContent: "center",
        alignItems: "center",
        zIndex: 2000,
    },
    editPanel: {
        backgroundColor: "#ffffff",
        borderRadius: 16,
        padding: 24,
        margin: 20,
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 10 },
        shadowOpacity: 0.3,
        shadowRadius: 20,
        elevation: 15,
    },
    editTitle: {
        fontSize: 18,
        fontWeight: "bold",
        color: "#212529",
        marginBottom: 16,
        textAlign: "center",
    },
    editOption: {
        backgroundColor: "#f8f9fa",
        borderRadius: 12,
        padding: 16,
        marginBottom: 12,
        borderWidth: 1,
        borderColor: "#dee2e6",
    },
    editOptionText: {
        fontSize: 16,
        color: "#495057",
        fontWeight: "500",
    },
});

export default ReadingViewScreen;
