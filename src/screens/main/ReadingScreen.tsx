import React, { useState } from "react";
import { View, Text, TouchableOpacity, ScrollView, StyleSheet } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { StackNavigationProp } from "@react-navigation/stack";
import { ReadingStackParamList } from "../../types/navigation";

type LanguageLevel = "A1" | "A2" | "B1" | "B2" | "C1" | "C2" | "CUSTOM";

interface UserPDF {
    id: string;
    title: string;
    uploadDate: string;
    progress: number;
}

type ReadingScreenNavigationProp = StackNavigationProp<ReadingStackParamList, "ReadingHome">;

interface ReadingScreenProps {
    navigation: ReadingScreenNavigationProp;
}

const ReadingScreen: React.FC<ReadingScreenProps> = ({ navigation }) => {
    const [selectedLevel, setSelectedLevel] = useState<LanguageLevel | null>(null);

    // Mock data - gerçek uygulamada bu veriler state management'tan gelecek
    const userPDFs: UserPDF[] = [
        { id: "1", title: "<PERSON><PERSON><PERSON><PERSON><PERSON> Hikayeler", uploadDate: "2 saat önce", progress: 75 },
        { id: "2", title: "Teknik Makale", uploadDate: "1 gün önce", progress: 45 },
    ];

    const levels = [
        {
            code: "A1" as LanguageLevel,
            name: "Başlangıç",
            description: "Temel kelimeler ve basit cümleler",
            color: ["#10b981", "#34d399"],
        },
        {
            code: "A2" as LanguageLevel,
            name: "Temel",
            description: "Günlük konuşmalar ve basit metinler",
            color: ["#3b82f6", "#60a5fa"],
        },
        {
            code: "B1" as LanguageLevel,
            name: "Orta Alt",
            description: "Genel konular ve açık metinler",
            color: ["#8b5cf6", "#a78bfa"],
        },
        {
            code: "B2" as LanguageLevel,
            name: "Orta Üst",
            description: "Karmaşık metinler ve soyut konular",
            color: ["#f59e0b", "#fbbf24"],
        },
        {
            code: "C1" as LanguageLevel,
            name: "İleri",
            description: "Uzun ve karmaşık metinler",
            color: ["#ef4444", "#f87171"],
        },
        {
            code: "C2" as LanguageLevel,
            name: "Uzman",
            description: "Akademik ve profesyonel metinler",
            color: ["#6366f1", "#818cf8"],
        },
        {
            code: "CUSTOM" as LanguageLevel,
            name: "Kendi PDF'im",
            description: "Kendi yüklediğiniz PDF dosyaları",
            color: ["#667eea", "#764ba2"],
        },
    ];

    const renderLevelSelection = () => (
        <View style={styles.levelSection}>
            <Text style={styles.sectionTitle}>🎯 Seviye Seçin</Text>
            <View style={styles.levelsContainer}>
                {levels.map((level) => (
                    <TouchableOpacity
                        key={level.code}
                        style={[
                            styles.levelCard,
                            selectedLevel === level.code && styles.selectedLevelCard,
                        ]}
                        onPress={() => setSelectedLevel(level.code)}
                    >
                        <LinearGradient
                            colors={level.color as [string, string]}
                            style={styles.levelGradient}
                            start={{ x: 0, y: 0 }}
                            end={{ x: 1, y: 1 }}
                        >
                            <Text style={styles.levelCode}>
                                {level.code === "CUSTOM" ? "📁" : level.code}
                            </Text>
                            <Text style={styles.levelName}>{level.name}</Text>
                            <Text style={styles.levelDescription}>{level.description}</Text>
                        </LinearGradient>
                    </TouchableOpacity>
                ))}
            </View>
        </View>
    );

    const renderCustomPDFSection = () => (
        <View style={styles.section}>
            <Text style={styles.sectionTitle}>📁 Kendi PDF'leriniz</Text>

            {/* User's uploaded PDFs */}
            {userPDFs.length > 0 && (
                <View style={styles.documentsContainer}>
                    {userPDFs.map((pdf) => (
                        <View key={pdf.id} style={styles.documentCard}>
                            <View style={styles.documentHeader}>
                                <View style={styles.documentIcon}>
                                    <Text style={styles.documentEmoji}>📄</Text>
                                </View>
                                <View style={styles.documentInfo}>
                                    <Text style={styles.documentTitle}>{pdf.title}</Text>
                                    <Text style={styles.documentSubtitle}>
                                        Son okuma: {pdf.uploadDate}
                                    </Text>
                                </View>
                            </View>
                            <View style={styles.progressContainer}>
                                <View style={styles.progressBar}>
                                    <View
                                        style={[styles.progressFill, { width: `${pdf.progress}%` }]}
                                    />
                                </View>
                                <Text style={styles.progressText}>%{pdf.progress} tamamlandı</Text>
                            </View>
                            <TouchableOpacity
                                style={styles.continueButton}
                                onPress={() =>
                                    navigation.navigate("ReadingView", {
                                        bookTitle: pdf.title,
                                        bookId: pdf.id,
                                        level: "CUSTOM",
                                    })
                                }
                            >
                                <Text style={styles.continueButtonText}>Devam Et</Text>
                            </TouchableOpacity>
                        </View>
                    ))}
                </View>
            )}

            {/* Upload new PDF section */}
            <LinearGradient
                colors={["#4facfe", "#00f2fe"]}
                style={styles.uploadCard}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
            >
                <View style={styles.uploadContent}>
                    <View style={styles.uploadIcon}>
                        <Text style={styles.uploadEmoji}>📄</Text>
                    </View>
                    <Text style={styles.uploadTitle}>
                        {userPDFs.length > 0 ? "Yeni PDF Yükle" : "İlk PDF'inizi Yükleyin"}
                    </Text>
                    <Text style={styles.uploadSubtitle}>
                        {userPDFs.length > 0
                            ? "Okumak istediğiniz yeni PDF dosyasını seçin"
                            : "Okumak istediğiniz PDF dosyasını seçin ve kelime öğrenmeye başlayın"}
                    </Text>
                    <TouchableOpacity style={styles.uploadButton}>
                        <Text style={styles.uploadButtonText}>📁 Dosya Seç</Text>
                    </TouchableOpacity>
                </View>
            </LinearGradient>
        </View>
    );

    const renderLevelBooks = () => {
        // Mock data for level-based books
        const levelBooks = [
            {
                id: "1",
                title: "Simple Stories for Beginners",
                difficulty: selectedLevel,
                pages: 45,
                rating: 4.5,
            },
            {
                id: "2",
                title: "Daily Conversations",
                difficulty: selectedLevel,
                pages: 32,
                rating: 4.2,
            },
            {
                id: "3",
                title: "Basic Grammar in Context",
                difficulty: selectedLevel,
                pages: 67,
                rating: 4.7,
            },
        ];

        return (
            <View style={styles.section}>
                <Text style={styles.sectionTitle}>📚 {selectedLevel} Seviyesi Kitaplar</Text>
                <View style={styles.documentsContainer}>
                    {levelBooks.map((book) => (
                        <View key={book.id} style={styles.documentCard}>
                            <View style={styles.documentHeader}>
                                <View style={styles.documentIcon}>
                                    <Text style={styles.documentEmoji}>📖</Text>
                                </View>
                                <View style={styles.documentInfo}>
                                    <Text style={styles.documentTitle}>{book.title}</Text>
                                    <Text style={styles.documentSubtitle}>
                                        {book.pages} sayfa • ⭐ {book.rating}
                                    </Text>
                                </View>
                            </View>
                            <TouchableOpacity
                                style={styles.startButton}
                                onPress={() =>
                                    navigation.navigate("ReadingView", {
                                        bookTitle: book.title,
                                        bookId: book.id,
                                        level: selectedLevel || undefined,
                                    })
                                }
                            >
                                <Text style={styles.startButtonText}>Okumaya Başla</Text>
                            </TouchableOpacity>
                        </View>
                    ))}
                </View>
            </View>
        );
    };

    const renderLevelContent = () => {
        if (selectedLevel === "CUSTOM") {
            return renderCustomPDFSection();
        } else {
            return renderLevelBooks();
        }
    };

    return (
        <LinearGradient colors={["#f8fafc", "#e2e8f0"]} style={styles.container}>
            <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
                <View style={styles.content}>
                    {/* Header */}
                    <View style={styles.header}>
                        <Text style={styles.title}>Okuma Merkezi 📖</Text>
                        <Text style={styles.subtitle}>Seviyenizi seçin ve okumaya başlayın</Text>
                    </View>

                    {/* Level Selection */}
                    {renderLevelSelection()}

                    {/* Content based on selected level */}
                    {selectedLevel && renderLevelContent()}

                    {/* Reading Tips */}
                    <LinearGradient
                        colors={["#a8edea", "#fed6e3"]}
                        style={styles.tipsCard}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 1 }}
                    >
                        <Text style={styles.tipsTitle}>💡 Okuma İpuçları</Text>
                        <View style={styles.tipsContainer}>
                            <View style={styles.tipItem}>
                                <Text style={styles.tipIcon}>👆</Text>
                                <Text style={styles.tipText}>
                                    Bilmediğiniz kelimelere dokunarak anlamlarını öğrenin
                                </Text>
                            </View>
                            <View style={styles.tipItem}>
                                <Text style={styles.tipIcon}>📝</Text>
                                <Text style={styles.tipText}>
                                    Önemli kelimeleri kelime listenize ekleyin
                                </Text>
                            </View>
                            <View style={styles.tipItem}>
                                <Text style={styles.tipIcon}>📅</Text>
                                <Text style={styles.tipText}>Düzenli okuma alışkanlığı edinin</Text>
                            </View>
                        </View>
                    </LinearGradient>
                </View>
            </ScrollView>
        </LinearGradient>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    scrollView: {
        flex: 1,
    },
    content: {
        paddingHorizontal: 24,
        paddingTop: 64,
        paddingBottom: 120,
    },
    header: {
        marginBottom: 32,
    },
    title: {
        fontSize: 28,
        fontWeight: "bold",
        color: "#1F2937",
        marginBottom: 8,
    },
    subtitle: {
        fontSize: 18,
        color: "#6B7280",
    },
    uploadCard: {
        borderRadius: 20,
        padding: 24,
        marginBottom: 32,
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 10 },
        shadowOpacity: 0.3,
        shadowRadius: 20,
        elevation: 10,
    },
    uploadContent: {
        alignItems: "center",
    },
    uploadIcon: {
        backgroundColor: "rgba(255, 255, 255, 0.2)",
        borderRadius: 50,
        padding: 16,
        marginBottom: 16,
    },
    uploadEmoji: {
        fontSize: 32,
    },
    uploadTitle: {
        fontSize: 20,
        fontWeight: "bold",
        color: "white",
        marginBottom: 8,
        textAlign: "center",
    },
    uploadSubtitle: {
        fontSize: 16,
        color: "rgba(255, 255, 255, 0.8)",
        textAlign: "center",
        marginBottom: 20,
    },
    uploadButton: {
        backgroundColor: "rgba(255, 255, 255, 0.2)",
        borderRadius: 12,
        paddingHorizontal: 24,
        paddingVertical: 12,
        borderWidth: 1,
        borderColor: "rgba(255, 255, 255, 0.3)",
    },
    uploadButtonText: {
        color: "white",
        fontSize: 16,
        fontWeight: "600",
    },
    section: {
        marginBottom: 32,
    },
    sectionTitle: {
        fontSize: 20,
        fontWeight: "bold",
        color: "#1F2937",
        marginBottom: 16,
    },
    documentsContainer: {
        gap: 16,
    },
    documentCard: {
        backgroundColor: "white",
        borderRadius: 16,
        padding: 20,
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.1,
        shadowRadius: 12,
        elevation: 5,
    },
    documentHeader: {
        flexDirection: "row",
        alignItems: "center",
        marginBottom: 16,
    },
    documentIcon: {
        backgroundColor: "#F3F4F6",
        borderRadius: 12,
        padding: 12,
        marginRight: 12,
    },
    documentEmoji: {
        fontSize: 20,
    },
    documentInfo: {
        flex: 1,
    },
    documentTitle: {
        fontSize: 16,
        fontWeight: "600",
        color: "#1F2937",
        marginBottom: 4,
    },
    documentSubtitle: {
        fontSize: 14,
        color: "#6B7280",
    },
    progressContainer: {
        marginBottom: 16,
    },
    progressBar: {
        height: 6,
        backgroundColor: "#E5E7EB",
        borderRadius: 3,
        marginBottom: 8,
    },
    progressFill: {
        height: "100%",
        backgroundColor: "#3B82F6",
        borderRadius: 3,
    },
    progressText: {
        fontSize: 12,
        color: "#6B7280",
        fontWeight: "500",
    },
    continueButton: {
        backgroundColor: "#3B82F6",
        borderRadius: 8,
        paddingVertical: 8,
        paddingHorizontal: 16,
        alignSelf: "flex-start",
    },
    continueButtonText: {
        color: "white",
        fontSize: 14,
        fontWeight: "600",
    },
    tipsCard: {
        borderRadius: 20,
        padding: 24,
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 0.2,
        shadowRadius: 16,
        elevation: 8,
    },
    tipsTitle: {
        fontSize: 20,
        fontWeight: "bold",
        color: "#1F2937",
        marginBottom: 16,
        textAlign: "center",
    },
    tipsContainer: {
        gap: 12,
    },
    tipItem: {
        flexDirection: "row",
        alignItems: "center",
    },
    tipIcon: {
        fontSize: 20,
        marginRight: 12,
    },
    tipText: {
        fontSize: 16,
        color: "#374151",
        flex: 1,
    },
    // Level selection styles
    levelSection: {
        marginBottom: 32,
    },
    levelsContainer: {
        flexDirection: "row",
        flexWrap: "wrap",
        gap: 12,
    },
    levelCard: {
        flex: 1,
        minWidth: "45%",
        borderRadius: 16,
        overflow: "hidden",
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.1,
        shadowRadius: 12,
        elevation: 5,
    },
    selectedLevelCard: {
        shadowColor: "#667eea",
        shadowOffset: { width: 0, height: 6 },
        shadowOpacity: 0.3,
        shadowRadius: 16,
        elevation: 8,
        transform: [{ scale: 1.02 }],
    },
    levelGradient: {
        padding: 16,
        alignItems: "center",
        minHeight: 100,
        justifyContent: "center",
    },
    levelCode: {
        fontSize: 24,
        fontWeight: "bold",
        color: "white",
        marginBottom: 4,
    },
    levelName: {
        fontSize: 14,
        fontWeight: "600",
        color: "white",
        marginBottom: 4,
        textAlign: "center",
    },
    levelDescription: {
        fontSize: 11,
        color: "rgba(255, 255, 255, 0.8)",
        textAlign: "center",
        lineHeight: 14,
    },
    // Start button for level books
    startButton: {
        backgroundColor: "#10b981",
        borderRadius: 8,
        paddingVertical: 8,
        paddingHorizontal: 16,
        alignSelf: "flex-start",
    },
    startButtonText: {
        color: "white",
        fontSize: 14,
        fontWeight: "600",
    },
});

export default ReadingScreen;
