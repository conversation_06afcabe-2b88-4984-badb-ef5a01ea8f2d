import React from "react";
import { View, Text, TouchableOpacity, ScrollView, StyleSheet } from "react-native";
import { LinearGradient } from "expo-linear-gradient";

const ReadingScreen: React.FC = () => {
    return (
        <LinearGradient colors={["#f8fafc", "#e2e8f0"]} style={styles.container}>
            <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
                <View style={styles.content}>
                    {/* Header */}
                    <View style={styles.header}>
                        <Text style={styles.title}>Okuma Merkezi 📖</Text>
                        <Text style={styles.subtitle}>
                            PDF dosyalarınızı yükleyin ve okumaya başlayın
                        </Text>
                    </View>

                    {/* Upload Section */}
                    <LinearGradient
                        colors={["#4facfe", "#00f2fe"]}
                        style={styles.uploadCard}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 1 }}
                    >
                        <View style={styles.uploadContent}>
                            <View style={styles.uploadIcon}>
                                <Text style={styles.uploadEmoji}>📄</Text>
                            </View>
                            <Text style={styles.uploadTitle}>PDF Dosyası Yükle</Text>
                            <Text style={styles.uploadSubtitle}>
                                Okumak istediğiniz PDF dosyasını seçin ve kelime öğrenmeye başlayın
                            </Text>
                            <TouchableOpacity style={styles.uploadButton}>
                                <Text style={styles.uploadButtonText}>📁 Dosya Seç</Text>
                            </TouchableOpacity>
                        </View>
                    </LinearGradient>

                    {/* Recent Documents */}
                    <View style={styles.section}>
                        <Text style={styles.sectionTitle}>📚 Son Okuduklarım</Text>

                        <View style={styles.documentsContainer}>
                            <View style={styles.documentCard}>
                                <View style={styles.documentHeader}>
                                    <View style={styles.documentIcon}>
                                        <Text style={styles.documentEmoji}>📄</Text>
                                    </View>
                                    <View style={styles.documentInfo}>
                                        <Text style={styles.documentTitle}>
                                            İngilizce Hikayeler
                                        </Text>
                                        <Text style={styles.documentSubtitle}>
                                            Son okuma: 2 saat önce
                                        </Text>
                                    </View>
                                </View>
                                <View style={styles.progressContainer}>
                                    <View style={styles.progressBar}>
                                        <View style={[styles.progressFill, { width: "75%" }]} />
                                    </View>
                                    <Text style={styles.progressText}>%75 tamamlandı</Text>
                                </View>
                                <TouchableOpacity style={styles.continueButton}>
                                    <Text style={styles.continueButtonText}>Devam Et</Text>
                                </TouchableOpacity>
                            </View>

                            <View style={styles.documentCard}>
                                <View style={styles.documentHeader}>
                                    <View style={styles.documentIcon}>
                                        <Text style={styles.documentEmoji}>📖</Text>
                                    </View>
                                    <View style={styles.documentInfo}>
                                        <Text style={styles.documentTitle}>Teknik Makale</Text>
                                        <Text style={styles.documentSubtitle}>
                                            Son okuma: 1 gün önce
                                        </Text>
                                    </View>
                                </View>
                                <View style={styles.progressContainer}>
                                    <View style={styles.progressBar}>
                                        <View style={[styles.progressFill, { width: "45%" }]} />
                                    </View>
                                    <Text style={styles.progressText}>%45 tamamlandı</Text>
                                </View>
                                <TouchableOpacity style={styles.continueButton}>
                                    <Text style={styles.continueButtonText}>Devam Et</Text>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>

                    {/* Reading Tips */}
                    <LinearGradient
                        colors={["#a8edea", "#fed6e3"]}
                        style={styles.tipsCard}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 1 }}
                    >
                        <Text style={styles.tipsTitle}>💡 Okuma İpuçları</Text>
                        <View style={styles.tipsContainer}>
                            <View style={styles.tipItem}>
                                <Text style={styles.tipIcon}>👆</Text>
                                <Text style={styles.tipText}>
                                    Bilmediğiniz kelimelere dokunarak anlamlarını öğrenin
                                </Text>
                            </View>
                            <View style={styles.tipItem}>
                                <Text style={styles.tipIcon}>📝</Text>
                                <Text style={styles.tipText}>
                                    Önemli kelimeleri kelime listenize ekleyin
                                </Text>
                            </View>
                            <View style={styles.tipItem}>
                                <Text style={styles.tipIcon}>📅</Text>
                                <Text style={styles.tipText}>Düzenli okuma alışkanlığı edinin</Text>
                            </View>
                        </View>
                    </LinearGradient>
                </View>
            </ScrollView>
        </LinearGradient>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    scrollView: {
        flex: 1,
    },
    content: {
        paddingHorizontal: 24,
        paddingTop: 64,
        paddingBottom: 120,
    },
    header: {
        marginBottom: 32,
    },
    title: {
        fontSize: 28,
        fontWeight: "bold",
        color: "#1F2937",
        marginBottom: 8,
    },
    subtitle: {
        fontSize: 18,
        color: "#6B7280",
    },
    uploadCard: {
        borderRadius: 20,
        padding: 24,
        marginBottom: 32,
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 10 },
        shadowOpacity: 0.3,
        shadowRadius: 20,
        elevation: 10,
    },
    uploadContent: {
        alignItems: "center",
    },
    uploadIcon: {
        backgroundColor: "rgba(255, 255, 255, 0.2)",
        borderRadius: 50,
        padding: 16,
        marginBottom: 16,
    },
    uploadEmoji: {
        fontSize: 32,
    },
    uploadTitle: {
        fontSize: 20,
        fontWeight: "bold",
        color: "white",
        marginBottom: 8,
        textAlign: "center",
    },
    uploadSubtitle: {
        fontSize: 16,
        color: "rgba(255, 255, 255, 0.8)",
        textAlign: "center",
        marginBottom: 20,
    },
    uploadButton: {
        backgroundColor: "rgba(255, 255, 255, 0.2)",
        borderRadius: 12,
        paddingHorizontal: 24,
        paddingVertical: 12,
        borderWidth: 1,
        borderColor: "rgba(255, 255, 255, 0.3)",
    },
    uploadButtonText: {
        color: "white",
        fontSize: 16,
        fontWeight: "600",
    },
    section: {
        marginBottom: 32,
    },
    sectionTitle: {
        fontSize: 20,
        fontWeight: "bold",
        color: "#1F2937",
        marginBottom: 16,
    },
    documentsContainer: {
        gap: 16,
    },
    documentCard: {
        backgroundColor: "white",
        borderRadius: 16,
        padding: 20,
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.1,
        shadowRadius: 12,
        elevation: 5,
    },
    documentHeader: {
        flexDirection: "row",
        alignItems: "center",
        marginBottom: 16,
    },
    documentIcon: {
        backgroundColor: "#F3F4F6",
        borderRadius: 12,
        padding: 12,
        marginRight: 12,
    },
    documentEmoji: {
        fontSize: 20,
    },
    documentInfo: {
        flex: 1,
    },
    documentTitle: {
        fontSize: 16,
        fontWeight: "600",
        color: "#1F2937",
        marginBottom: 4,
    },
    documentSubtitle: {
        fontSize: 14,
        color: "#6B7280",
    },
    progressContainer: {
        marginBottom: 16,
    },
    progressBar: {
        height: 6,
        backgroundColor: "#E5E7EB",
        borderRadius: 3,
        marginBottom: 8,
    },
    progressFill: {
        height: "100%",
        backgroundColor: "#3B82F6",
        borderRadius: 3,
    },
    progressText: {
        fontSize: 12,
        color: "#6B7280",
        fontWeight: "500",
    },
    continueButton: {
        backgroundColor: "#3B82F6",
        borderRadius: 8,
        paddingVertical: 8,
        paddingHorizontal: 16,
        alignSelf: "flex-start",
    },
    continueButtonText: {
        color: "white",
        fontSize: 14,
        fontWeight: "600",
    },
    tipsCard: {
        borderRadius: 20,
        padding: 24,
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 0.2,
        shadowRadius: 16,
        elevation: 8,
    },
    tipsTitle: {
        fontSize: 20,
        fontWeight: "bold",
        color: "#1F2937",
        marginBottom: 16,
        textAlign: "center",
    },
    tipsContainer: {
        gap: 12,
    },
    tipItem: {
        flexDirection: "row",
        alignItems: "center",
    },
    tipIcon: {
        fontSize: 20,
        marginRight: 12,
    },
    tipText: {
        fontSize: 16,
        color: "#374151",
        flex: 1,
    },
});

export default ReadingScreen;
