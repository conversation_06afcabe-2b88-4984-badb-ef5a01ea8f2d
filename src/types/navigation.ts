import { NavigatorScreenParams } from '@react-navigation/native';

export type RootStackParamList = {
  Auth: NavigatorScreenParams<AuthStackParamList>;
  Main: NavigatorScreenParams<MainTabParamList>;
};

export type AuthStackParamList = {
  Login: undefined;
  Register: undefined;
  ForgotPassword: undefined;
};

export type MainTabParamList = {
  Home: undefined;
  Reading: undefined;
  WordList: undefined;
  Quiz: undefined;
  Profile: undefined;
};

export type ReadingStackParamList = {
  ReadingHome: undefined;
  ReadingView: { documentId: string };
  WordDetail: { word: string };
};

declare global {
  namespace ReactNavigation {
    interface RootParamList extends RootStackParamList {}
  }
}
