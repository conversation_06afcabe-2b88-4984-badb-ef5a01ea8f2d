import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, TextInputProps } from 'react-native';

interface InputProps extends TextInputProps {
  label?: string;
  error?: string;
  icon?: string;
  rightIcon?: string;
  onRightIconPress?: () => void;
  variant?: 'default' | 'floating';
}

const Input: React.FC<InputProps> = ({
  label,
  error,
  icon,
  rightIcon,
  onRightIconPress,
  variant = 'default',
  value,
  onChangeText,
  placeholder,
  secureTextEntry,
  ...props
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [isSecure, setIsSecure] = useState(secureTextEntry);

  const handleRightIconPress = () => {
    if (secureTextEntry) {
      setIsSecure(!isSecure);
    } else if (onRightIconPress) {
      onRightIconPress();
    }
  };

  const getRightIcon = () => {
    if (secureTextEntry) {
      return isSecure ? '👁️' : '🙈';
    }
    return rightIcon;
  };

  if (variant === 'floating') {
    return (
      <View className="mb-4">
        <View className={`
          relative
          border-2 rounded-xl
          ${isFocused ? 'border-blue-500' : error ? 'border-red-500' : 'border-gray-200'}
          ${isFocused ? 'shadow-lg shadow-blue-500/20' : ''}
          transition-all duration-200
        `}>
          {icon && (
            <View className="absolute left-4 top-1/2 transform -translate-y-1/2 z-10">
              <Text className="text-lg">{icon}</Text>
            </View>
          )}
          
          <TextInput
            value={value}
            onChangeText={onChangeText}
            placeholder={placeholder}
            secureTextEntry={isSecure}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            className={`
              px-4 py-4 text-gray-900 text-base
              ${icon ? 'pl-12' : ''}
              ${getRightIcon() ? 'pr-12' : ''}
              rounded-xl
            `}
            placeholderTextColor="#9CA3AF"
            {...props}
          />
          
          {label && (
            <Text className={`
              absolute left-4 bg-white px-2 text-sm font-medium
              transition-all duration-200
              ${isFocused || value ? '-top-2' : 'top-4'}
              ${isFocused ? 'text-blue-500' : error ? 'text-red-500' : 'text-gray-500'}
            `}>
              {label}
            </Text>
          )}
          
          {getRightIcon() && (
            <TouchableOpacity
              onPress={handleRightIconPress}
              className="absolute right-4 top-1/2 transform -translate-y-1/2"
            >
              <Text className="text-lg">{getRightIcon()}</Text>
            </TouchableOpacity>
          )}
        </View>
        
        {error && (
          <Text className="text-red-500 text-sm mt-1 ml-1">
            {error}
          </Text>
        )}
      </View>
    );
  }

  return (
    <View className="mb-4">
      {label && (
        <Text className="text-gray-700 font-medium mb-2 ml-1">
          {label}
        </Text>
      )}
      
      <View className={`
        relative
        border-2 rounded-xl bg-white
        ${isFocused ? 'border-blue-500' : error ? 'border-red-500' : 'border-gray-200'}
        ${isFocused ? 'shadow-lg shadow-blue-500/20' : 'shadow-sm'}
        transition-all duration-200
      `}>
        {icon && (
          <View className="absolute left-4 top-1/2 transform -translate-y-1/2 z-10">
            <Text className="text-lg text-gray-400">{icon}</Text>
          </View>
        )}
        
        <TextInput
          value={value}
          onChangeText={onChangeText}
          placeholder={placeholder}
          secureTextEntry={isSecure}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          className={`
            px-4 py-4 text-gray-900 text-base
            ${icon ? 'pl-12' : ''}
            ${getRightIcon() ? 'pr-12' : ''}
            rounded-xl
          `}
          placeholderTextColor="#9CA3AF"
          {...props}
        />
        
        {getRightIcon() && (
          <TouchableOpacity
            onPress={handleRightIconPress}
            className="absolute right-4 top-1/2 transform -translate-y-1/2"
          >
            <Text className="text-lg text-gray-400">{getRightIcon()}</Text>
          </TouchableOpacity>
        )}
      </View>
      
      {error && (
        <Text className="text-red-500 text-sm mt-1 ml-1">
          {error}
        </Text>
      )}
    </View>
  );
};

export default Input;
