import React from 'react';
import { View, TouchableOpacity, ViewStyle } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';

interface CardProps {
  children: React.ReactNode;
  variant?: 'default' | 'gradient' | 'glass' | 'elevated';
  onPress?: () => void;
  className?: string;
  gradientColors?: string[];
  style?: ViewStyle;
}

const Card: React.FC<CardProps> = ({
  children,
  variant = 'default',
  onPress,
  className = '',
  gradientColors = ['#667eea', '#764ba2'],
  style,
}) => {
  const getCardStyles = () => {
    const baseStyles = 'rounded-2xl overflow-hidden';
    
    switch (variant) {
      case 'gradient':
        return `${baseStyles} shadow-xl shadow-blue-500/25`;
      case 'glass':
        return `${baseStyles} bg-white/80 backdrop-blur-md border border-white/20 shadow-xl`;
      case 'elevated':
        return `${baseStyles} bg-white shadow-2xl shadow-gray-500/20`;
      default:
        return `${baseStyles} bg-white shadow-lg shadow-gray-200/50 border border-gray-100`;
    }
  };

  const cardStyles = getCardStyles();

  if (variant === 'gradient') {
    const CardContent = (
      <LinearGradient
        colors={gradientColors}
        className={`${cardStyles} ${className}`}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={style}
      >
        <View className="p-6">
          {children}
        </View>
      </LinearGradient>
    );

    if (onPress) {
      return (
        <TouchableOpacity
          onPress={onPress}
          activeOpacity={0.9}
          className="active:scale-95 transition-transform duration-200"
        >
          {CardContent}
        </TouchableOpacity>
      );
    }

    return CardContent;
  }

  const CardContent = (
    <View className={`${cardStyles} ${className} p-6`} style={style}>
      {children}
    </View>
  );

  if (onPress) {
    return (
      <TouchableOpacity
        onPress={onPress}
        activeOpacity={0.9}
        className="active:scale-95 transition-transform duration-200"
      >
        {CardContent}
      </TouchableOpacity>
    );
  }

  return CardContent;
};

export default Card;
