import React from 'react';
import { TouchableOpacity, Text, ActivityIndicator, View } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';

interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline' | 'danger';
  size?: 'small' | 'medium' | 'large';
  loading?: boolean;
  disabled?: boolean;
  icon?: string;
  fullWidth?: boolean;
}

const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  loading = false,
  disabled = false,
  icon,
  fullWidth = false,
}) => {
  const getVariantStyles = () => {
    switch (variant) {
      case 'primary':
        return {
          gradient: ['#667eea', '#764ba2'],
          textColor: 'text-white',
          shadowColor: 'shadow-blue-500/25',
        };
      case 'secondary':
        return {
          gradient: ['#f093fb', '#f5576c'],
          textColor: 'text-white',
          shadowColor: 'shadow-pink-500/25',
        };
      case 'danger':
        return {
          gradient: ['#ff6b6b', '#ee5a52'],
          textColor: 'text-white',
          shadowColor: 'shadow-red-500/25',
        };
      case 'outline':
        return {
          gradient: ['transparent', 'transparent'],
          textColor: 'text-blue-600',
          shadowColor: 'shadow-gray-200',
          border: 'border-2 border-blue-600',
        };
      default:
        return {
          gradient: ['#667eea', '#764ba2'],
          textColor: 'text-white',
          shadowColor: 'shadow-blue-500/25',
        };
    }
  };

  const getSizeStyles = () => {
    switch (size) {
      case 'small':
        return {
          padding: 'px-4 py-2',
          textSize: 'text-sm',
          height: 'h-10',
        };
      case 'large':
        return {
          padding: 'px-8 py-4',
          textSize: 'text-lg',
          height: 'h-14',
        };
      default:
        return {
          padding: 'px-6 py-3',
          textSize: 'text-base',
          height: 'h-12',
        };
    }
  };

  const variantStyles = getVariantStyles();
  const sizeStyles = getSizeStyles();
  const isDisabled = disabled || loading;

  const buttonContent = (
    <View className={`flex-row items-center justify-center ${sizeStyles.padding}`}>
      {loading && (
        <ActivityIndicator 
          size="small" 
          color={variant === 'outline' ? '#2563eb' : 'white'} 
          className="mr-2" 
        />
      )}
      {icon && !loading && (
        <Text className={`${sizeStyles.textSize} mr-2`}>{icon}</Text>
      )}
      <Text className={`${variantStyles.textColor} ${sizeStyles.textSize} font-semibold`}>
        {loading ? 'Yükleniyor...' : title}
      </Text>
    </View>
  );

  if (variant === 'outline') {
    return (
      <TouchableOpacity
        onPress={onPress}
        disabled={isDisabled}
        className={`
          ${sizeStyles.height}
          ${fullWidth ? 'w-full' : ''}
          ${variantStyles.border}
          rounded-xl
          ${variantStyles.shadowColor}
          shadow-lg
          ${isDisabled ? 'opacity-50' : 'active:scale-95'}
          transition-all duration-200
        `}
        activeOpacity={0.8}
      >
        {buttonContent}
      </TouchableOpacity>
    );
  }

  return (
    <TouchableOpacity
      onPress={onPress}
      disabled={isDisabled}
      className={`
        ${sizeStyles.height}
        ${fullWidth ? 'w-full' : ''}
        rounded-xl
        ${variantStyles.shadowColor}
        shadow-lg
        ${isDisabled ? 'opacity-50' : 'active:scale-95'}
        transition-all duration-200
      `}
      activeOpacity={0.8}
    >
      <LinearGradient
        colors={variantStyles.gradient}
        className={`flex-1 rounded-xl justify-center`}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        {buttonContent}
      </LinearGradient>
    </TouchableOpacity>
  );
};

export default Button;
