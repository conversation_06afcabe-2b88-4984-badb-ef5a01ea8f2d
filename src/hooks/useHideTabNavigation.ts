import { useFocusEffect } from '@react-navigation/native';
import React from 'react';

interface TabBarStyle {
    backgroundColor?: string;
    borderTopWidth?: number;
    elevation?: number;
    shadowColor?: string;
    shadowOffset?: { width: number; height: number };
    shadowOpacity?: number;
    shadowRadius?: number;
    paddingBottom?: number;
    paddingTop?: number;
    height?: number;
    borderTopLeftRadius?: number;
    borderTopRightRadius?: number;
    position?: 'absolute' | 'relative';
    display?: 'none' | 'flex';
}

interface UseHideTabNavigationOptions {
    navigation: any;
    customTabBarStyle?: TabBarStyle;
}

/**
 * Custom hook to hide/show tab navigation when screen is focused/unfocused
 * This provides a reusable solution for any screen that needs to hide the main navigation
 * 
 * @param navigation - Navigation object from the screen component
 * @param customTabBarStyle - Optional custom tab bar style to restore when leaving screen
 */
export const useHideTabNavigation = ({ 
    navigation, 
    customTabBarStyle 
}: UseHideTabNavigationOptions) => {
    
    // Default tab bar style that matches the app's design
    const defaultTabBarStyle: TabBarStyle = {
        backgroundColor: "white",
        borderTopWidth: 0,
        elevation: 25,
        shadowColor: "#000",
        shadowOffset: { width: 0, height: -8 },
        shadowOpacity: 0.12,
        shadowRadius: 24,
        paddingBottom: 20,
        paddingTop: 16,
        height: 100,
        borderTopLeftRadius: 24,
        borderTopRightRadius: 24,
        position: "absolute",
    };

    // Merge custom style with default style
    const tabBarStyle = { ...defaultTabBarStyle, ...customTabBarStyle };

    useFocusEffect(
        React.useCallback(() => {
            const parentNavigator = navigation.getParent();
            
            if (parentNavigator) {
                // Hide tab bar when entering the screen
                parentNavigator.setOptions({
                    tabBarStyle: { display: 'none' }
                });
            }

            // Restore tab bar when leaving the screen
            return () => {
                if (parentNavigator) {
                    parentNavigator.setOptions({
                        tabBarStyle: tabBarStyle
                    });
                }
            };
        }, [navigation, tabBarStyle])
    );
};

export default useHideTabNavigation;
