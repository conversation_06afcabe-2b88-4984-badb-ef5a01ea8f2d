<svg width="300" height="120" viewBox="0 0 300 120" xmlns="http://www.w3.org/2000/svg">
  <!-- Gradient Definitions -->
  <defs>
    <!-- Main gradient for the book -->
    <linearGradient id="bookGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    
    <!-- Secondary gradient for accent elements -->
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4facfe;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00f2fe;stop-opacity:1" />
    </linearGradient>
    
    <!-- Text gradient -->
    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#2d3748;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#4a5568;stop-opacity:1" />
    </linearGradient>
    
    <!-- Language bubble gradient -->
    <linearGradient id="bubbleGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f093fb;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#f5576c;stop-opacity:0.8" />
    </linearGradient>
  </defs>
  
  <!-- Background circle for depth -->
  <circle cx="60" cy="60" r="45" fill="url(#accentGradient)" opacity="0.1"/>
  
  <!-- Main Book Icon -->
  <g transform="translate(20, 20)">
    <!-- Book cover -->
    <rect x="15" y="15" width="50" height="70" rx="4" ry="4" fill="url(#bookGradient)" stroke="#5a67d8" stroke-width="1"/>
    
    <!-- Book spine -->
    <rect x="12" y="18" width="6" height="64" rx="2" ry="2" fill="#5a67d8" opacity="0.8"/>
    
    <!-- Book pages -->
    <rect x="18" y="18" width="44" height="64" rx="2" ry="2" fill="#ffffff" stroke="#e2e8f0" stroke-width="1"/>
    
    <!-- Text lines on the page -->
    <line x1="25" y1="30" x2="55" y2="30" stroke="#a0aec0" stroke-width="1.5" stroke-linecap="round"/>
    <line x1="25" y1="38" x2="50" y2="38" stroke="#a0aec0" stroke-width="1.5" stroke-linecap="round"/>
    <line x1="25" y1="46" x2="53" y2="46" stroke="#a0aec0" stroke-width="1.5" stroke-linecap="round"/>
    <line x1="25" y1="54" x2="48" y2="54" stroke="#a0aec0" stroke-width="1.5" stroke-linecap="round"/>
    
    <!-- Highlighted word (representing translation feature) -->
    <rect x="25" y="60" width="20" height="8" rx="2" ry="2" fill="url(#accentGradient)" opacity="0.3"/>
    <line x1="25" y1="64" x2="45" y2="64" stroke="url(#accentGradient)" stroke-width="2" stroke-linecap="round"/>
  </g>
  
  <!-- Language Translation Bubbles -->
  <g transform="translate(75, 25)">
    <!-- English bubble -->
    <circle cx="0" cy="15" r="12" fill="url(#bubbleGradient)" opacity="0.9"/>
    <text x="0" y="20" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="10" font-weight="bold">EN</text>
    
    <!-- Arrow -->
    <path d="M 15 15 L 25 15 M 20 10 L 25 15 L 20 20" stroke="url(#accentGradient)" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
    
    <!-- Turkish bubble -->
    <circle cx="40" cy="15" r="12" fill="url(#bubbleGradient)" opacity="0.9"/>
    <text x="40" y="20" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="10" font-weight="bold">TR</text>
  </g>
  
  <!-- App Name -->
  <g transform="translate(140, 35)">
    <text x="0" y="0" fill="url(#textGradient)" font-family="Arial, sans-serif" font-size="28" font-weight="bold">Lingo</text>
    <text x="0" y="35" fill="url(#textGradient)" font-family="Arial, sans-serif" font-size="28" font-weight="300">Texts</text>
  </g>
  
  <!-- Tagline -->
  <text x="140" y="85" fill="#718096" font-family="Arial, sans-serif" font-size="12" font-style="italic">PDF ile Dil Öğrenin</text>
  
  <!-- Decorative elements -->
  <g opacity="0.6">
    <!-- Small dots for modern touch -->
    <circle cx="250" cy="30" r="2" fill="url(#accentGradient)"/>
    <circle cx="260" cy="25" r="1.5" fill="url(#bubbleGradient)"/>
    <circle cx="270" cy="35" r="1" fill="url(#bookGradient)"/>
    
    <!-- Small language symbols -->
    <text x="245" y="95" fill="url(#accentGradient)" font-family="Arial, sans-serif" font-size="16" opacity="0.7">📚</text>
    <text x="265" y="95" fill="url(#bubbleGradient)" font-family="Arial, sans-serif" font-size="16" opacity="0.7">🌐</text>
  </g>
</svg>
